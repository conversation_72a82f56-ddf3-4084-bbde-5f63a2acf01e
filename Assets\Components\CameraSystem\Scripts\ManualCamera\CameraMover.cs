using System.Collections;
using CoreTech.CoroutineUtilities;
using Unity.Cinemachine; // Updated namespace
using UnityEngine;

namespace CameraSystem.ManualCamera
{
    public class CameraMover
    {
        private CinemachineCamera camera; // Updated to use CinemachineCamera

        private float minZoom = 2f;
        private float maxZoom = 50f;
        private float zoom = 5f;
        private float smoothSwitchTime = 1.5f;

        private Transform proxyFollow;
        private Coroutine currentCoroutine;
        private CinemachineInputAxisController inputAxisController;
        private bool isDragging = false;

        public CameraMover(CinemachineCamera camera)
        {
            this.camera = camera;

            var orbitalFollow = camera.GetComponent<CinemachineOrbitalFollow>();
            if (orbitalFollow == null)
            {
                Debug.LogError("Cinemachine Orbital Follow is null");
                return;
            }


            inputAxisController = camera.GetComponent<CinemachineInputAxisController>();

            if (inputAxisController == null)
            {
                Debug.LogError("Cinemachine Input Axis Controller is null");
                return;
            }

            // Disable automatic mouse input - we want drag-based input only
            inputAxisController.enabled = false;
            Debug.Log("CameraMover initialized - inputAxisController disabled for drag-only input");

            zoom = orbitalFollow.Radius;

            proxyFollow = new GameObject("ProxyFollow").transform;
            proxyFollow.SetParent(camera.transform.parent);
        }

        public void SetCameraTarget(Transform target)
        {
            if (camera.Follow == null)
            {
                // This is the first set so no need to do a smooth switch
                camera.Follow = target;
                camera.LookAt = target;
                return;
            }


            if (currentCoroutine != null)
            {
                CoroutineProvider.StopCoroutine(currentCoroutine);
            }

            currentCoroutine = CoroutineProvider.StartCoroutine(SmoothSwitch(target));
        }

        /// <summary>
        /// This is a temporary solution to enable smooth switch to follow target.
        /// When we refactor the camera system, we should implement a more robust solution.
        /// </summary>
        private IEnumerator SmoothSwitch(Transform target)
        {
            // Note: inputAxisController is already disabled for drag-only input

            proxyFollow.position = camera.Follow.position;

            Vector3 startPos = proxyFollow.position;

            camera.Follow = proxyFollow;
            camera.LookAt = target;

            float elapsedTime = 0f;
            while (elapsedTime < smoothSwitchTime)
            {
                float t = elapsedTime / smoothSwitchTime;
                proxyFollow.position = Vector3.Slerp(startPos, target.position, t);

                elapsedTime += Time.deltaTime;
                yield return null;
            }

            camera.Follow = target;
            camera.LookAt = target;
            currentCoroutine = null;
            // Note: Keep inputAxisController disabled for drag-only input
        }

        public void AdjustZoom(float zoomDelta)
        {
            float previousZoom = zoom;
            zoom -= zoomDelta;
            zoom = Mathf.Clamp(zoom, minZoom, maxZoom);

            Debug.Log($"Manual zoom triggered: delta={zoomDelta}, zoom changed from {previousZoom} to {zoom}");

            var orbitalFollow = camera.GetComponent<CinemachineOrbitalFollow>();
            if (orbitalFollow == null)
            {
                Debug.LogError("Cinemachine Orbital Follow is null");
                return;
            }
            zoom = orbitalFollow.Radius;
        }

        public void HorizontalRotation(float value)
        {
            Debug.Log($"CameraMover.HorizontalRotation called: value={value}, isDragging={isDragging}");

            // Only apply rotation when dragging
            if (isDragging)
            {
                var panTilt = camera.GetComponent<CinemachinePanTilt>();
                if (panTilt != null)
                {
                    Debug.Log($"Applying horizontal rotation: old value={panTilt.PanAxis.Value}, adding={value}");
                    panTilt.PanAxis.Value += value;
                    Debug.Log($"New horizontal rotation value: {panTilt.PanAxis.Value}");
                }
                else
                {
                    Debug.LogError("CinemachinePanTilt component is null!");
                }
            }
            else
            {
                Debug.Log("Horizontal rotation blocked - not dragging");
            }
        }

        public void VerticalRotation(float value)
        {
            Debug.Log($"CameraMover.VerticalRotation called: value={value}, isDragging={isDragging}");

            // Only apply rotation when dragging
            if (isDragging)
            {
                var panTilt = camera.GetComponent<CinemachinePanTilt>();
                if (panTilt != null)
                {
                    Debug.Log($"Applying vertical rotation: old value={panTilt.TiltAxis.Value}, adding={value}");
                    panTilt.TiltAxis.Value += value;
                    Debug.Log($"New vertical rotation value: {panTilt.TiltAxis.Value}");
                }
                else
                {
                    Debug.LogError("CinemachinePanTilt component is null!");
                }
            }
            else
            {
                Debug.Log("Vertical rotation blocked - not dragging");
            }
        }

        public void StartDrag()
        {
            Debug.Log("CameraMover.StartDrag called - enabling rotation");
            isDragging = true;
        }

        public void EndDrag()
        {
            Debug.Log("CameraMover.EndDrag called - disabling rotation");
            isDragging = false;
        }
    }
}
